{"action": "", "method": "post", "elements": [{"tag": "input", "type": "hidden", "name": "csrf_token", "id": "csrf_token", "value": "IjVkZjg2NWRlNDFiYzE4MDg5MzkwNzRmNjVmZDNmNzE0NWQ5Njg3YWUi.aDyACg.TbzO3OSKrrL9K7iDXnrS8JMiErk", "placeholder": "", "required": false}, {"tag": "input", "type": "text", "name": "item_name", "id": "item_name", "value": "", "placeholder": "", "required": true}, {"tag": "select", "type": "", "name": "class_select", "id": "class_select", "value": "", "placeholder": "", "required": false, "options": [{"value": "Class", "text": "Class"}, {"value": "1", "text": "Warrior"}, {"value": "2", "text": "Cleric"}, {"value": "4", "text": "<PERSON><PERSON><PERSON>"}, {"value": "8", "text": "<PERSON>"}, {"value": "16", "text": "Shadow Knight"}, {"value": "32", "text": "Druid"}, {"value": "64", "text": "<PERSON>"}, {"value": "128", "text": "Bard"}, {"value": "256", "text": "Rogue"}, {"value": "512", "text": "Shaman"}, {"value": "1024", "text": "Necromancer"}, {"value": "2048", "text": "<PERSON>"}, {"value": "4096", "text": "Magician"}, {"value": "8192", "text": "Enchanter"}, {"value": "16384", "text": "<PERSON><PERSON>"}]}, {"tag": "select", "type": "", "name": "race_select", "id": "race_select", "value": "", "placeholder": "", "required": false, "options": [{"value": "Race", "text": "Race"}, {"value": "8192", "text": "<PERSON><PERSON>"}, {"value": "4096", "text": "<PERSON><PERSON><PERSON>"}, {"value": "2048", "text": "Gnome"}, {"value": "1024", "text": "<PERSON><PERSON>"}, {"value": "512", "text": "Ogre"}, {"value": "256", "text": "Troll"}, {"value": "128", "text": "<PERSON><PERSON><PERSON>"}, {"value": "64", "text": "Half Elf"}, {"value": "32", "text": "Dark Elf"}, {"value": "16", "text": "High Elf"}, {"value": "8", "text": "<PERSON>"}, {"value": "4", "text": "<PERSON><PERSON><PERSON>"}, {"value": "2", "text": "Barbarian"}, {"value": "1", "text": "Human"}]}, {"tag": "select", "type": "", "name": "slot_select", "id": "slot_select", "value": "", "placeholder": "", "required": false, "options": [{"value": "Slot", "text": "Slot"}, {"value": "2097152", "text": "Ammo"}, {"value": "1048576", "text": "Waist"}, {"value": "524288", "text": "Feet"}, {"value": "262144", "text": "Legs"}, {"value": "131072", "text": "Chest"}, {"value": "98304", "text": "Fingers"}, {"value": "16384", "text": "Secondary"}, {"value": "8192", "text": "Primary"}, {"value": "4096", "text": "Hands"}, {"value": "2048", "text": "Range"}, {"value": "1536", "text": "Wrists"}, {"value": "256", "text": "Back"}, {"value": "128", "text": "Arms"}, {"value": "64", "text": "Shoulders"}, {"value": "32", "text": "Neck"}, {"value": "18", "text": "Ears"}, {"value": "8", "text": "Face"}, {"value": "4", "text": "Head"}, {"value": "2", "text": "Ear"}, {"value": "1", "text": "Charm"}]}, {"tag": "select", "type": "", "name": "type_select", "id": "type_select", "value": "", "placeholder": "", "required": false, "options": [{"value": "Type", "text": "Item Type"}, {"value": "0", "text": "1HS"}, {"value": "1", "text": "2HS"}, {"value": "2", "text": "Piercing"}, {"value": "3", "text": "1HB"}, {"value": "4", "text": "2HB"}, {"value": "5", "text": "Archery"}, {"value": "7", "text": "Throwing range items"}, {"value": "8", "text": "Shield"}, {"value": "10", "text": "Armor"}, {"value": "11", "text": "Gems"}, {"value": "12", "text": "Lockpicks"}, {"value": "14", "text": "Food"}, {"value": "15", "text": "Drink"}, {"value": "16", "text": "Light"}, {"value": "17", "text": "Combinable"}, {"value": "18", "text": "Bandages"}, {"value": "19", "text": "Throwing"}, {"value": "20", "text": "<PERSON><PERSON>"}, {"value": "21", "text": "Potion"}, {"value": "23", "text": "Wind Instrument"}, {"value": "24", "text": "Stringed Instrument"}, {"value": "25", "text": "Brass Instrument"}, {"value": "26", "text": "Percussion Instrument"}, {"value": "27", "text": "Arrow"}, {"value": "29", "text": "Jewelry"}, {"value": "30", "text": "Skull"}, {"value": "31", "text": "<PERSON><PERSON>"}, {"value": "32", "text": "Note"}, {"value": "33", "text": "Key"}, {"value": "34", "text": "Coin"}, {"value": "35", "text": "2H Piercing"}, {"value": "36", "text": "Fishing Pole"}, {"value": "37", "text": "Fishing Bait"}, {"value": "38", "text": "Alcohol"}, {"value": "39", "text": "Key (bis)"}, {"value": "40", "text": "<PERSON>mp<PERSON>"}, {"value": "42", "text": "Poison"}, {"value": "45", "text": "<PERSON>"}, {"value": "52", "text": "Charm"}, {"value": "54", "text": "Augmentation"}]}, {"tag": "select", "type": "", "name": "stat1_select", "id": "stat1_select", "value": "", "placeholder": "", "required": false, "options": [{"value": "Stat", "text": "Stat"}, {"value": "hp", "text": "Hit Points"}, {"value": "mana", "text": "<PERSON><PERSON>"}, {"value": "ac", "text": "AC"}, {"value": "attack", "text": "Attack"}, {"value": "aagi", "text": "Agility"}, {"value": "acha", "text": "Charisma"}, {"value": "adex", "text": "Dexterity"}, {"value": "aint", "text": "Intelligence"}, {"value": "asta", "text": "Stamina"}, {"value": "astr", "text": "Strength"}, {"value": "awis", "text": "Wisdom"}, {"value": "damage", "text": "Damage"}, {"value": "delay", "text": "Delay"}]}, {"tag": "select", "type": "", "name": "sel1_select", "id": "sel1_select", "value": "", "placeholder": "", "required": false, "options": [{"value": ">", "text": ">"}, {"value": ">=", "text": ">="}, {"value": "=", "text": "="}, {"value": "<=", "text": "<="}, {"value": "<", "text": "<"}]}, {"tag": "input", "type": "number", "name": "stat1_int", "id": "stat1_int", "value": "0", "placeholder": "", "required": false}, {"tag": "select", "type": "", "name": "stat2_select", "id": "stat2_select", "value": "", "placeholder": "", "required": false, "options": [{"value": "Stat", "text": "Stat"}, {"value": "hp", "text": "Hit Points"}, {"value": "mana", "text": "<PERSON><PERSON>"}, {"value": "ac", "text": "AC"}, {"value": "attack", "text": "Attack"}, {"value": "aagi", "text": "Agility"}, {"value": "acha", "text": "Charisma"}, {"value": "adex", "text": "Dexterity"}, {"value": "aint", "text": "Intelligence"}, {"value": "asta", "text": "Stamina"}, {"value": "astr", "text": "Strength"}, {"value": "awis", "text": "Wisdom"}, {"value": "damage", "text": "Damage"}, {"value": "delay", "text": "Delay"}]}, {"tag": "select", "type": "", "name": "sel2_select", "id": "sel2_select", "value": "", "placeholder": "", "required": false, "options": [{"value": ">", "text": ">"}, {"value": ">=", "text": ">="}, {"value": "=", "text": "="}, {"value": "<=", "text": "<="}, {"value": "<", "text": "<"}]}, {"tag": "input", "type": "number", "name": "stat2_int", "id": "stat2_int", "value": "0", "placeholder": "", "required": false}, {"tag": "select", "type": "", "name": "resist_select", "id": "resist_select", "value": "", "placeholder": "", "required": false, "options": [{"value": "Resist", "text": "Resist"}, {"value": "mr", "text": "Resist Magic"}, {"value": "fr", "text": "Resist Fire"}, {"value": "cr", "text": "Resist Cold"}, {"value": "pr", "text": "Resist Poison"}, {"value": "dr", "text": "Resist Disease"}]}, {"tag": "select", "type": "", "name": "res_select", "id": "res_select", "value": "", "placeholder": "", "required": false, "options": [{"value": ">", "text": ">"}, {"value": ">=", "text": ">="}, {"value": "=", "text": "="}, {"value": "<=", "text": "<="}, {"value": "<", "text": "<"}]}, {"tag": "input", "type": "number", "name": "res_int", "id": "res_int", "value": "0", "placeholder": "", "required": false}, {"tag": "input", "type": "checkbox", "name": "has_proc", "id": "has_proc", "value": "y", "placeholder": "", "required": false}, {"tag": "input", "type": "checkbox", "name": "has_click", "id": "has_click", "value": "y", "placeholder": "", "required": false}, {"tag": "input", "type": "checkbox", "name": "has_focus", "id": "has_focus", "value": "y", "placeholder": "", "required": false}, {"tag": "input", "type": "checkbox", "name": "has_worn", "id": "has_worn", "value": "y", "placeholder": "", "required": false}, {"tag": "input", "type": "text", "name": "effect_name", "id": "effect_name", "value": "", "placeholder": "", "required": true}, {"tag": "select", "type": "", "name": "bag_select", "id": "bag_select", "value": "", "placeholder": "", "required": false, "options": [{"value": "Container", "text": "Container"}, {"value": "1", "text": "Just a Bag"}, {"value": "2", "text": "Quiver"}, {"value": "3", "text": "Pouch"}, {"value": "4", "text": "Pouch"}, {"value": "5", "text": "Backpack"}, {"value": "6", "text": "Tupperware"}, {"value": "7", "text": "Box"}, {"value": "8", "text": "<PERSON><PERSON><PERSON>"}, {"value": "9", "text": "Alchemy"}, {"value": "10", "text": "Tinkering"}, {"value": "11", "text": "Research"}, {"value": "12", "text": "Poison making"}, {"value": "13", "text": "Special quests"}, {"value": "14", "text": "Baking: Mixing"}, {"value": "15", "text": "Baking: Cooking"}, {"value": "16", "text": "Tailoring: Sewing <PERSON>"}, {"value": "18", "text": "Fletching"}, {"value": "19", "text": "Brewing"}, {"value": "20", "text": "Jewelry"}, {"value": "24", "text": "Wizard Research"}, {"value": "25", "text": "Mage Research"}, {"value": "26", "text": "Necro Research"}, {"value": "27", "text": "Enchanter Research"}, {"value": "28", "text": "Plat Storage"}, {"value": "29", "text": "Practice Research"}, {"value": "30", "text": "Pottery"}, {"value": "41", "text": "Tailoring: Vale"}, {"value": "42", "text": "Tailoring: <PERSON><PERSON><PERSON>"}, {"value": "43", "text": "Tailoring: <PERSON><PERSON><PERSON>"}, {"value": "44", "text": "Fletching"}, {"value": "46", "text": "Fishing"}, {"value": "51", "text": "<PERSON><PERSON><PERSON>"}]}, {"tag": "input", "type": "number", "name": "bagslots", "id": "bagslots", "value": "0", "placeholder": "", "required": false}, {"tag": "input", "type": "number", "name": "wr", "id": "wr", "value": "0", "placeholder": "", "required": false}, {"tag": "input", "type": "checkbox", "name": "legacy", "id": "legacy", "value": "y", "placeholder": "", "required": false}, {"tag": "input", "type": "checkbox", "name": "table_view", "id": "table_view", "value": "y", "placeholder": "", "required": false}, {"tag": "input", "type": "submit", "name": "submit", "id": "submit", "value": "Search", "placeholder": "", "required": false}]}