import requests
from bs4 import BeautifulSoup
from typing import Dict, List, Optional, Any
import time
import logging
from config import Config

logger = logging.getLogger(__name__)

class WebScraper:
    """Handle requests to external websites and data extraction"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': Config.USER_AGENT,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
        self.last_request_time = 0
        self.min_request_interval = 1.0  # Minimum seconds between requests
    
    def _rate_limit(self):
        """Implement basic rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            time.sleep(self.min_request_interval - time_since_last)
        self.last_request_time = time.time()
    
    def make_request(self, url: str, params: Dict[str, Any] = None, method: str = 'GET') -> Optional[requests.Response]:
        """Make a request to the target website with rate limiting"""
        try:
            self._rate_limit()
            
            if method.upper() == 'GET':
                response = self.session.get(url, params=params, timeout=Config.REQUEST_TIMEOUT)
            elif method.upper() == 'POST':
                response = self.session.post(url, data=params, timeout=Config.REQUEST_TIMEOUT)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed: {e}")
            return None
    
    def search_items(self, search_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Search for items on the PQDI website
        """
        url = "https://www.pqdi.cc/items"

        try:
            # First, get the page to extract CSRF token
            initial_response = self.make_request(url)
            if not initial_response:
                return {
                    'success': False,
                    'error': 'Failed to connect to PQDI website',
                    'data': []
                }

            # Extract CSRF token
            soup = BeautifulSoup(initial_response.content, 'html.parser')
            csrf_input = soup.find('input', {'name': 'csrf_token'})
            csrf_token = csrf_input.get('value') if csrf_input else ''

            # Prepare form data for PQDI
            form_data = self._prepare_pqdi_form_data(search_params, csrf_token)

            # Make the search request
            response = self.make_request(url, form_data, method='POST')
            if not response:
                return {
                    'success': False,
                    'error': 'Failed to perform search on PQDI website',
                    'data': []
                }

            # Parse the response
            soup = BeautifulSoup(response.content, 'html.parser')
            items = self._parse_search_results(soup)

            return {
                'success': True,
                'error': None,
                'data': items,
                'total_count': len(items)
            }

        except Exception as e:
            logger.error(f"Search failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'data': []
            }
    
    def _prepare_pqdi_form_data(self, search_params: Dict[str, Any], csrf_token: str) -> Dict[str, Any]:
        """
        Prepare form data for PQDI website submission
        """
        form_data = {
            'csrf_token': csrf_token,
            'item_name': search_params.get('item_name', ''),
            'class_select': search_params.get('class_select', 'Class'),
            'race_select': search_params.get('race_select', 'Race'),
            'slot_select': search_params.get('slot_select', 'Slot'),
            'type_select': search_params.get('type_select', 'Type'),
            'stat1_select': search_params.get('stat1_select', 'Stat'),
            'sel1_select': search_params.get('sel1_select', '>'),
            'stat1_int': search_params.get('stat1_int', '0'),
            'stat2_select': search_params.get('stat2_select', 'Stat'),
            'sel2_select': search_params.get('sel2_select', '>'),
            'stat2_int': search_params.get('stat2_int', '0'),
            'resist_select': search_params.get('resist_select', 'Resist'),
            'res_select': search_params.get('res_select', '>'),
            'res_int': search_params.get('res_int', '0'),
            'effect_name': search_params.get('effect_name', ''),
            'bag_select': search_params.get('bag_select', 'Container'),
            'bagslots': search_params.get('bagslots', '0'),
            'wr': search_params.get('wr', '0'),
            'submit': 'Search'
        }

        # Handle checkboxes - only include if checked
        if search_params.get('has_proc'):
            form_data['has_proc'] = 'y'
        if search_params.get('has_click'):
            form_data['has_click'] = 'y'
        if search_params.get('has_focus'):
            form_data['has_focus'] = 'y'
        if search_params.get('has_worn'):
            form_data['has_worn'] = 'y'
        if search_params.get('legacy'):
            form_data['legacy'] = 'y'
        if search_params.get('table_view'):
            form_data['table_view'] = 'y'

        return form_data

    def _parse_search_results(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """
        Parse search results from the PQDI website HTML
        """
        items = []

        # Look for the results table
        table = soup.find('table', class_='table')
        if not table:
            logger.warning("No results table found")
            return items

        # Get table rows (skip header)
        rows = table.find_all('tr')[1:]  # Skip header row

        for row in rows:
            cells = row.find_all('td')
            if len(cells) < 3:  # Ensure we have enough cells
                continue

            try:
                item = self._parse_item_row(cells)
                if item:
                    items.append(item)
            except Exception as e:
                logger.warning(f"Error parsing item row: {e}")
                continue

        logger.info(f"Parsed {len(items)} items from PQDI results")
        return items

    def _parse_item_row(self, cells) -> Dict[str, Any]:
        """
        Parse a single item row from the PQDI results table
        """
        if len(cells) < 3:
            return None

        # Basic item structure - will need to be adjusted based on actual table structure
        item = {
            'name': '',
            'description': '',
            'stats': {},
            'url': '',
            'image_url': ''
        }

        # Parse item name and link (usually in first cell)
        name_cell = cells[0]
        name_link = name_cell.find('a')
        if name_link:
            item['name'] = name_link.get_text().strip()
            item['url'] = 'https://www.pqdi.cc' + name_link.get('href', '')
        else:
            item['name'] = name_cell.get_text().strip()

        # Parse additional columns based on table structure
        # This will need to be customized based on the actual PQDI table format
        for i, cell in enumerate(cells[1:], 1):
            cell_text = cell.get_text().strip()
            if cell_text and cell_text != '-':
                item[f'column_{i}'] = cell_text

        return item if item['name'] else None

    def get_available_filters(self) -> Dict[str, List[str]]:
        """
        Get available filters from the PQDI form structure
        """
        return {
            'class_select': [
                {'value': '1', 'label': 'Warrior'},
                {'value': '2', 'label': 'Cleric'},
                {'value': '4', 'label': 'Paladin'},
                {'value': '8', 'label': 'Ranger'},
                {'value': '16', 'label': 'Shadow Knight'},
                {'value': '32', 'label': 'Druid'},
                {'value': '64', 'label': 'Monk'},
                {'value': '128', 'label': 'Bard'},
                {'value': '256', 'label': 'Rogue'},
                {'value': '512', 'label': 'Shaman'},
                {'value': '1024', 'label': 'Necromancer'},
                {'value': '2048', 'label': 'Wizard'},
                {'value': '4096', 'label': 'Magician'},
                {'value': '8192', 'label': 'Enchanter'},
                {'value': '16384', 'label': 'Beastlord'}
            ],
            'race_select': [
                {'value': '1', 'label': 'Human'},
                {'value': '2', 'label': 'Barbarian'},
                {'value': '4', 'label': 'Erudite'},
                {'value': '8', 'label': 'Wood Elf'},
                {'value': '16', 'label': 'High Elf'},
                {'value': '32', 'label': 'Dark Elf'},
                {'value': '64', 'label': 'Half Elf'},
                {'value': '128', 'label': 'Dwarf'},
                {'value': '256', 'label': 'Troll'},
                {'value': '512', 'label': 'Ogre'},
                {'value': '1024', 'label': 'Halfling'},
                {'value': '2048', 'label': 'Gnome'},
                {'value': '4096', 'label': 'Iksar'},
                {'value': '8192', 'label': 'Vah Shir'}
            ],
            'slot_select': [
                {'value': '1', 'label': 'Charm'},
                {'value': '2', 'label': 'Ear'},
                {'value': '4', 'label': 'Head'},
                {'value': '8', 'label': 'Face'},
                {'value': '18', 'label': 'Ears'},
                {'value': '32', 'label': 'Neck'},
                {'value': '64', 'label': 'Shoulders'},
                {'value': '128', 'label': 'Arms'},
                {'value': '256', 'label': 'Back'},
                {'value': '1536', 'label': 'Wrists'},
                {'value': '2048', 'label': 'Range'},
                {'value': '4096', 'label': 'Hands'},
                {'value': '8192', 'label': 'Primary'},
                {'value': '16384', 'label': 'Secondary'},
                {'value': '98304', 'label': 'Fingers'},
                {'value': '131072', 'label': 'Chest'},
                {'value': '262144', 'label': 'Legs'},
                {'value': '524288', 'label': 'Feet'},
                {'value': '1048576', 'label': 'Waist'},
                {'value': '2097152', 'label': 'Ammo'}
            ]
        }
