/* Modern Dark Theme CSS */
:root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3a3a3a;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #808080;
    --accent-primary: #4a9eff;
    --accent-secondary: #6c5ce7;
    --success: #00b894;
    --warning: #fdcb6e;
    --error: #e17055;
    --border: #404040;
    --shadow: rgba(0, 0, 0, 0.3);
    --radius: 8px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Navigation */
.navbar {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--accent-primary);
}

.nav-links {
    display: flex;
    gap: 1rem;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: var(--radius);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link:hover {
    color: var(--text-primary);
    background-color: var(--bg-tertiary);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Search Section */
.search-section {
    background-color: var(--bg-secondary);
    border-radius: var(--radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px var(--shadow);
}

.page-title {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--accent-primary);
}

.page-subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

/* Search Form */
.search-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.search-input-group {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-input {
    flex: 1;
    padding: 1rem;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(74, 158, 255, 0.2);
}

.search-button {
    padding: 1rem 2rem;
    background-color: var(--accent-primary);
    color: white;
    border: none;
    border-radius: var(--radius);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.search-button:hover {
    background-color: #3a8ae6;
    transform: translateY(-1px);
}

/* Filter Sections */
.filter-section {
    background-color: var(--bg-tertiary);
    border-radius: var(--radius);
    padding: 1.5rem;
    border: 1px solid var(--border);
}

.filter-title {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--accent-secondary);
}

.filter-title.collapsible {
    cursor: pointer;
    user-select: none;
    transition: var(--transition);
}

.filter-title.collapsible:hover {
    color: var(--accent-primary);
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    transition: var(--transition);
}

.filter-grid.collapsed {
    max-height: 0;
    overflow: hidden;
    opacity: 0;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: var(--text-secondary);
}

.filter-group input,
.filter-group select {
    padding: 0.75rem;
    background-color: var(--bg-primary);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    color: var(--text-primary);
    transition: var(--transition);
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(74, 158, 255, 0.2);
}

.range-inputs {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.range-inputs input {
    flex: 1;
}

.value-filter-inputs {
    display: flex;
    gap: 0.5rem;
}

.value-filter-inputs select {
    flex: 1;
}

.value-filter-inputs input {
    flex: 1;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.checkbox-group .checkbox-label {
    margin-top: 0;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    font-weight: 500;
}

.btn-primary {
    background-color: var(--accent-primary);
    color: white;
}

.btn-primary:hover {
    background-color: #3a8ae6;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border);
}

.btn-secondary:hover {
    background-color: var(--bg-primary);
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Results Section */
.results-section {
    background-color: var(--bg-secondary);
    border-radius: var(--radius);
    padding: 2rem;
    box-shadow: 0 4px 6px var(--shadow);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.results-title {
    font-size: 1.75rem;
    color: var(--accent-primary);
}

.results-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.results-actions {
    display: flex;
    gap: 0.5rem;
}

/* Statistics Panel */
.statistics-panel {
    background-color: var(--bg-tertiary);
    border-radius: var(--radius);
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border);
}

.statistics-title {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    user-select: none;
    transition: var(--transition);
}

.statistics-title:hover {
    color: var(--accent-primary);
}

.statistics-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    transition: var(--transition);
}

.statistics-content.collapsed {
    max-height: 0;
    overflow: hidden;
    opacity: 0;
}

.stat-item {
    background-color: var(--bg-primary);
    padding: 1rem;
    border-radius: var(--radius);
    border: 1px solid var(--border);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--accent-primary);
}

/* Results Grid */
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.result-item {
    background-color: var(--bg-tertiary);
    border-radius: var(--radius);
    padding: 1.5rem;
    border: 1px solid var(--border);
    transition: var(--transition);
    cursor: pointer;
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

.result-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px var(--shadow);
    border-color: var(--accent-primary);
}

.result-item-image {
    width: 32px;
    height: 32px;
    flex-shrink: 0;
    border-radius: 4px;
    background-color: var(--bg-primary);
}

.result-item-content {
    flex: 1;
    min-width: 0;
}

.result-item-id {
    font-size: 0.8rem;
    color: var(--text-muted);
    font-style: italic;
}

.result-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--accent-primary);
}

.result-description {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    font-size: 0.9rem;
    line-height: 1.4;
}

.result-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 0.5rem;
}

.result-stat {
    text-align: center;
    padding: 0.5rem;
    background-color: var(--bg-primary);
    border-radius: var(--radius);
    border: 1px solid var(--border);
}

.result-stat-label {
    font-size: 0.75rem;
    color: var(--text-muted);
    display: block;
}

.result-stat-value {
    font-weight: 600;
    color: var(--text-primary);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination-btn {
    padding: 0.5rem 1rem;
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
}

.pagination-btn:hover {
    background-color: var(--bg-primary);
}

.pagination-btn.active {
    background-color: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    transition: var(--transition);
}

.loading-spinner {
    text-align: center;
    color: var(--text-primary);
}

.loading-spinner i {
    font-size: 3rem;
    color: var(--accent-primary);
    margin-bottom: 1rem;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1001;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.toast {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    padding: 1rem 1.5rem;
    border-radius: var(--radius);
    border-left: 4px solid var(--accent-primary);
    box-shadow: 0 4px 6px var(--shadow);
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.toast.success {
    border-left-color: var(--success);
}

.toast.warning {
    border-left-color: var(--warning);
}

.toast.error {
    border-left-color: var(--error);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-muted {
    color: var(--text-muted);
}

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }

/* Footer */
.footer {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border);
    padding: 1rem 0;
    margin-top: auto;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    text-align: center;
    color: var(--text-muted);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .search-input-group {
        flex-direction: column;
    }
    
    .filter-grid {
        grid-template-columns: 1fr;
    }
    
    .results-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .results-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .page-title {
        font-size: 2rem;
    }
}
