from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
import logging
import json
import io
from datetime import datetime
from config import Config
from modules.web_scraper import WebScraper
from modules.filter_engine import FilterEngine
from modules.data_processor import DataProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.config.from_object(Config)
CORS(app)

# Initialize modules
web_scraper = WebScraper()
filter_engine = FilterEngine()
data_processor = DataProcessor()

@app.route('/')
def index():
    """Main application page"""
    # Get website filters to pass to template
    website_filters = web_scraper.get_available_filters()
    return render_template('index.html', website_filters=website_filters)

@app.route('/filters')
def filters_page():
    """Filters information page"""
    available_filters = filter_engine.get_available_filters()
    return render_template('filters.html', filters=available_filters)

@app.route('/about')
def about_page():
    """About page"""
    return render_template('about.html')

@app.route('/api/website-filters')
def get_website_filters():
    """Get available filters from the target website"""
    try:
        filters = web_scraper.get_available_filters()
        return jsonify({
            'success': True,
            'filters': filters
        })
    except Exception as e:
        logger.error(f"Error getting website filters: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/custom-filters')
def get_custom_filters():
    """Get available custom filters"""
    try:
        filters = filter_engine.get_available_filters()
        return jsonify({
            'success': True,
            'filters': filters
        })
    except Exception as e:
        logger.error(f"Error getting custom filters: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/search', methods=['POST'])
def search_items():
    """Search for items with filters"""
    try:
        search_data = request.get_json()
        if not search_data:
            return jsonify({
                'success': False,
                'error': 'No search data provided'
            }), 400

        logger.info(f"Search request: {search_data}")

        # Check cache first
        cache_key = f"search_{hash(json.dumps(search_data, sort_keys=True))}"
        cached_result = data_processor.get_cached_data(cache_key)
        if cached_result:
            logger.info("Returning cached results")
            return jsonify({
                'success': True,
                'data': cached_result,
                'cached': True
            })

        # Separate website filters from custom filters
        website_filters = {}
        custom_filters = {}
        
        for key, value in search_data.items():
            if key.startswith('website_'):
                website_filters[key[8:]] = value  # Remove 'website_' prefix
            elif key in ['query', 'search_term']:
                website_filters[key] = value
            else:
                custom_filters[key] = value

        # Search the target website
        logger.info(f"Website filters: {website_filters}")
        search_result = web_scraper.search_items(website_filters)
        
        if not search_result['success']:
            return jsonify({
                'success': False,
                'error': search_result['error']
            }), 500

        # Normalize the data
        normalized_items = []
        for item in search_result['data']:
            normalized_item = data_processor.normalize_item_data(item)
            normalized_items.append(normalized_item)

        # Apply custom filters
        logger.info(f"Custom filters: {custom_filters}")
        if custom_filters:
            filtered_items = filter_engine.apply_filters(normalized_items, custom_filters)
        else:
            filtered_items = normalized_items

        # Aggregate results with statistics
        final_result = data_processor.aggregate_results(filtered_items)

        # Cache the result
        data_processor.cache_data(cache_key, final_result, Config.CACHE_TIMEOUT)

        logger.info(f"Search completed: {len(filtered_items)} items found")
        
        return jsonify({
            'success': True,
            'data': final_result
        })

    except Exception as e:
        logger.error(f"Search error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/export', methods=['POST'])
def export_results():
    """Export search results"""
    try:
        export_data = request.get_json()
        if not export_data or 'items' not in export_data:
            return jsonify({
                'success': False,
                'error': 'No items to export'
            }), 400

        items = export_data['items']
        format_type = export_data.get('format', 'json')

        # Export the data
        exported_data = data_processor.export_results(items, format_type)
        
        # Create file-like object
        output = io.StringIO(exported_data)
        output.seek(0)
        
        # Determine filename and mimetype
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        if format_type.lower() == 'json':
            filename = f'search_results_{timestamp}.json'
            mimetype = 'application/json'
        elif format_type.lower() == 'csv':
            filename = f'search_results_{timestamp}.csv'
            mimetype = 'text/csv'
        else:
            filename = f'search_results_{timestamp}.txt'
            mimetype = 'text/plain'

        # Convert StringIO to BytesIO for send_file
        byte_output = io.BytesIO(exported_data.encode('utf-8'))
        byte_output.seek(0)

        return send_file(
            byte_output,
            as_attachment=True,
            download_name=filename,
            mimetype=mimetype
        )

    except Exception as e:
        logger.error(f"Export error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {error}")
    return render_template('500.html'), 500

if __name__ == '__main__':
    logger.info(f"Starting Game Item Search application on {Config.HOST}:{Config.PORT}")
    logger.info(f"Debug mode: {Config.DEBUG}")
    
    app.run(
        host=Config.HOST,
        port=Config.PORT,
        debug=Config.DEBUG
    )
