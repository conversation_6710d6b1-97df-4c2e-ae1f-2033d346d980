#!/usr/bin/env python3
"""
Test script to verify PQDI search functionality
"""

import requests
from bs4 import BeautifulSoup
import json

def test_pqdi_search():
    """Test a simple search on PQDI to understand the response format"""
    
    url = "https://www.pqdi.cc/items"
    
    try:
        # Get initial page for CSRF token
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        initial_response = session.get(url)
        initial_response.raise_for_status()
        
        soup = BeautifulSoup(initial_response.content, 'html.parser')
        csrf_input = soup.find('input', {'name': 'csrf_token'})
        csrf_token = csrf_input.get('value') if csrf_input else ''
        
        print(f"CSRF Token: {csrf_token[:20]}...")
        
        # Prepare a simple search (search for "sword")
        form_data = {
            'csrf_token': csrf_token,
            'item_name': 'sword',
            'class_select': 'Class',
            'race_select': 'Race',
            'slot_select': 'Slot',
            'type_select': 'Type',
            'stat1_select': 'Stat',
            'sel1_select': '>',
            'stat1_int': '0',
            'stat2_select': 'Stat',
            'sel2_select': '>',
            'stat2_int': '0',
            'resist_select': 'Resist',
            'res_select': '>',
            'res_int': '0',
            'effect_name': '',
            'bag_select': 'Container',
            'bagslots': '0',
            'wr': '0',
            'submit': 'Search'
        }
        
        print("Performing search for 'sword'...")
        
        # Make the search request
        response = session.post(url, data=form_data)
        response.raise_for_status()
        
        # Parse the response
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Look for results table
        table = soup.find('table', class_='table')
        if table:
            print("Found results table!")
            
            # Get header row to understand structure
            header_row = table.find('tr')
            if header_row:
                headers = [th.get_text().strip() for th in header_row.find_all(['th', 'td'])]
                print(f"Table headers: {headers}")
            
            # Get first few data rows
            rows = table.find_all('tr')[1:6]  # Skip header, get first 5 results
            print(f"Found {len(rows)} result rows (showing first 5)")
            
            for i, row in enumerate(rows):
                cells = row.find_all('td')
                if cells:
                    print(f"\nRow {i+1}:")
                    for j, cell in enumerate(cells):
                        cell_text = cell.get_text().strip()
                        # Check for links
                        link = cell.find('a')
                        if link:
                            href = link.get('href', '')
                            print(f"  Column {j}: {cell_text} (Link: {href})")
                        else:
                            print(f"  Column {j}: {cell_text}")
        else:
            print("No results table found")
            # Look for any indication of results or errors
            content = soup.get_text()
            if "No items found" in content or "no results" in content.lower():
                print("Search returned no results")
            else:
                print("Unknown response format")
                # Save response for debugging
                with open('pqdi_response.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print("Response saved to pqdi_response.html")
    
    except Exception as e:
        print(f"Error testing PQDI search: {e}")

if __name__ == "__main__":
    test_pqdi_search()
