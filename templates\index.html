{% extends "base.html" %}

{% block title %}Game Item Search - Home{% endblock %}

{% block content %}
<div class="container">
    <div class="search-section">
        <h1 class="page-title">
            <i class="fas fa-search"></i>
            Game Item Search
        </h1>
        <p class="page-subtitle">Search and filter items with advanced custom filters</p>

        <!-- Search Form -->
        <form id="search-form" class="search-form">
            <div class="search-input-group">
                <input type="text" id="search-query" name="query" placeholder="Enter search terms..." class="search-input">
                <button type="submit" class="search-button">
                    <i class="fas fa-search"></i>
                    Search
                </button>
            </div>

            <!-- Website Filters Section -->
            <div class="filter-section">
                <h3 class="filter-title">
                    <i class="fas fa-filter"></i>
                    Website Filters
                </h3>
                <div id="website-filters" class="filter-grid">
                    <!-- Website filters rendered from server -->
                    {% if website_filters %}
                        <!-- Basic Search -->
                        <div class="filter-group">
                            <label for="search-term">Search Term:</label>
                            <input type="text" id="search-term" name="search" placeholder="Enter item name...">
                        </div>

                        <!-- Class Filter -->
                        {% if website_filters.get('class_select') %}
                        <div class="filter-group">
                            <label for="class-filter">Class:</label>
                            <select id="class-filter" name="class">
                                <option value="">All Classes</option>
                                {% for option in website_filters.class_select %}
                                <option value="{{ option.value }}">{{ option.label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        {% endif %}

                        <!-- Race Filter -->
                        {% if website_filters.get('race_select') %}
                        <div class="filter-group">
                            <label for="race-filter">Race:</label>
                            <select id="race-filter" name="race">
                                <option value="">All Races</option>
                                {% for option in website_filters.race_select %}
                                <option value="{{ option.value }}">{{ option.label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        {% endif %}

                        <!-- Slot Filter -->
                        {% if website_filters.get('slot_select') %}
                        <div class="filter-group">
                            <label for="slot-filter">Slot:</label>
                            <select id="slot-filter" name="slot">
                                <option value="">All Slots</option>
                                {% for option in website_filters.slot_select %}
                                <option value="{{ option.value }}">{{ option.label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        {% endif %}


                    {% else %}
                        <div class="filter-placeholder">
                            <p>Website filters not available</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Custom Filters Section -->
            <div class="filter-section">
                <h3 class="filter-title">
                    <i class="fas fa-cogs"></i>
                    Custom Filters
                </h3>
                <div class="filter-grid">
                    <!-- Name Contains Filter -->
                    <div class="filter-group">
                        <label for="name-contains">Name Contains:</label>
                        <input type="text" id="name-contains" name="name_contains" placeholder="e.g., sword">
                    </div>

                    <!-- Level Range Filter -->
                    <div class="filter-group">
                        <label>Level Range:</label>
                        <div class="range-inputs">
                            <input type="number" id="level-min" name="level_min" placeholder="Min" min="1">
                            <span>to</span>
                            <input type="number" id="level-max" name="level_max" placeholder="Max" min="1">
                        </div>
                    </div>

                    <!-- Exclude Keywords Filter -->
                    <div class="filter-group">
                        <label for="exclude-keywords">Exclude Keywords:</label>
                        <input type="text" id="exclude-keywords" name="exclude_keywords" placeholder="broken, damaged (comma-separated)">
                    </div>

                    <!-- Regex Pattern Filter -->
                    <div class="filter-group">
                        <label for="name-regex">Name Pattern (Regex):</label>
                        <input type="text" id="name-regex" name="name_regex" placeholder="^Epic.*Sword$">
                    </div>

                    <!-- Sort Options -->
                    <div class="filter-group">
                        <label for="sort-field">Sort By:</label>
                        <select id="sort-field" name="sort_field">
                            <option value="">No sorting</option>
                            <option value="name">Name</option>
                            <option value="level">Level</option>
                            <option value="price">Price</option>
                            <option value="damage">Damage</option>
                        </select>
                        <label class="checkbox-label">
                            <input type="checkbox" id="sort-reverse" name="sort_reverse">
                            Descending
                        </label>
                    </div>
                </div>
            </div>

            <!-- Advanced Filters (Collapsible) -->
            <div class="filter-section">
                <h3 class="filter-title collapsible" data-target="advanced-filters">
                    <i class="fas fa-chevron-down"></i>
                    Advanced Filters
                </h3>
                <div id="advanced-filters" class="filter-grid collapsed">
                    <!-- Min/Max Value Filters -->
                    <div class="filter-group">
                        <label>Minimum Value Filter:</label>
                        <div class="value-filter-inputs">
                            <select id="min-value-field" name="min_value_field">
                                <option value="">Select field</option>
                                <option value="damage">Damage</option>
                                <option value="price">Price</option>
                                <option value="ac">Armor Class</option>
                                <option value="hp">Hit Points</option>
                            </select>
                            <input type="number" id="min-value" name="min_value" placeholder="Min value">
                        </div>
                    </div>

                    <div class="filter-group">
                        <label>Maximum Value Filter:</label>
                        <div class="value-filter-inputs">
                            <select id="max-value-field" name="max_value_field">
                                <option value="">Select field</option>
                                <option value="damage">Damage</option>
                                <option value="price">Price</option>
                                <option value="ac">Armor Class</option>
                                <option value="hp">Hit Points</option>
                            </select>
                            <input type="number" id="max-value" name="max_value" placeholder="Max value">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    Search Items
                </button>
                <button type="button" id="clear-filters" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    Clear Filters
                </button>
                <button type="button" id="export-results" class="btn btn-secondary" disabled>
                    <i class="fas fa-download"></i>
                    Export Results
                </button>
            </div>
        </form>
    </div>

    <!-- Results Section -->
    <div id="results-section" class="results-section hidden">
        <div class="results-header">
            <h2 class="results-title">Search Results</h2>
            <div class="results-stats">
                <span id="results-count">0 items found</span>
                <div class="results-actions">
                    <button id="toggle-view" class="btn btn-small">
                        <i class="fas fa-th"></i>
                        Grid View
                    </button>
                </div>
            </div>
        </div>

        <!-- Results Statistics -->
        <div id="results-statistics" class="statistics-panel collapsed">
            <h3 class="statistics-title collapsible" data-target="statistics-content">
                <i class="fas fa-chart-bar"></i>
                Statistics
            </h3>
            <div id="statistics-content" class="statistics-content">
                <!-- Statistics will be populated dynamically -->
            </div>
        </div>

        <!-- Results Grid -->
        <div id="results-grid" class="results-grid">
            <!-- Results will be populated dynamically -->
        </div>

        <!-- Pagination -->
        <div id="pagination" class="pagination hidden">
            <!-- Pagination will be populated dynamically -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing app...');
    if (typeof initializeApp === 'function') {
        initializeApp();
    } else {
        console.error('initializeApp function not found');
        // Fallback: try to load filters directly
        setTimeout(function() {
            fetch('/api/website-filters')
                .then(response => response.json())
                .then(data => {
                    console.log('Fallback filters loaded:', data);
                    if (data.success && data.filters) {
                        // Simple fallback rendering
                        const container = document.getElementById('website-filters');
                        if (container) {
                            container.innerHTML = '<p>Website filters loaded successfully! Check console for details.</p>';
                        }
                    }
                })
                .catch(error => console.error('Fallback filter loading failed:', error));
        }, 1000);
    }
});
</script>
{% endblock %}
