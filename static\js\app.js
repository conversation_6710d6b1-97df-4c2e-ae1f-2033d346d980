// Modern JavaScript Application
class GameItemSearchApp {
    constructor() {
        this.currentResults = [];
        this.currentPage = 1;
        this.itemsPerPage = 20;
        this.isGridView = true;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadWebsiteFilters();
    }

    bindEvents() {
        // Search form submission
        const searchForm = document.getElementById('search-form');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.performSearch();
            });
        }

        // Clear filters button
        const clearButton = document.getElementById('clear-filters');
        if (clearButton) {
            clearButton.addEventListener('click', () => this.clearFilters());
        }

        // Export results button
        const exportButton = document.getElementById('export-results');
        if (exportButton) {
            exportButton.addEventListener('click', () => this.exportResults());
        }

        // Toggle view button
        const toggleViewButton = document.getElementById('toggle-view');
        if (toggleViewButton) {
            toggleViewButton.addEventListener('click', () => this.toggleView());
        }

        // Collapsible sections
        document.querySelectorAll('.collapsible').forEach(element => {
            element.addEventListener('click', () => this.toggleCollapse(element));
        });

        // Real-time search (debounced)
        const searchInput = document.getElementById('search-query');
        if (searchInput) {
            let debounceTimer;
            searchInput.addEventListener('input', () => {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    if (searchInput.value.length > 2) {
                        this.performSearch();
                    }
                }, 500);
            });
        }
    }

    async loadWebsiteFilters() {
        try {
            const response = await fetch('/api/website-filters');
            if (response.ok) {
                const filters = await response.json();
                this.renderWebsiteFilters(filters);
            }
        } catch (error) {
            console.error('Failed to load website filters:', error);
        }
    }

    renderWebsiteFilters(filters) {
        const container = document.getElementById('website-filters');
        if (!container || !filters || Object.keys(filters).length === 0) {
            return;
        }

        container.innerHTML = '';

        // Create PQDI-specific filter layout
        this.createPQDIFilters(container, filters);
    }

    createPQDIFilters(container, filters) {
        // Item Name Filter
        const nameGroup = document.createElement('div');
        nameGroup.className = 'filter-group';
        nameGroup.innerHTML = `
            <label for="website-item_name">Item Name:</label>
            <input type="text" id="website-item_name" name="website_item_name" placeholder="Enter item name...">
        `;
        container.appendChild(nameGroup);

        // Class Filter
        if (filters.class_select) {
            const classGroup = document.createElement('div');
            classGroup.className = 'filter-group';

            const classLabel = document.createElement('label');
            classLabel.textContent = 'Class:';
            classLabel.setAttribute('for', 'website-class_select');

            const classSelect = document.createElement('select');
            classSelect.id = 'website-class_select';
            classSelect.name = 'website_class_select';

            const defaultOption = document.createElement('option');
            defaultOption.value = 'Class';
            defaultOption.textContent = 'All Classes';
            classSelect.appendChild(defaultOption);

            filters.class_select.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.label;
                classSelect.appendChild(optionElement);
            });

            classGroup.appendChild(classLabel);
            classGroup.appendChild(classSelect);
            container.appendChild(classGroup);
        }

        // Race Filter
        if (filters.race_select) {
            const raceGroup = document.createElement('div');
            raceGroup.className = 'filter-group';

            const raceLabel = document.createElement('label');
            raceLabel.textContent = 'Race:';
            raceLabel.setAttribute('for', 'website-race_select');

            const raceSelect = document.createElement('select');
            raceSelect.id = 'website-race_select';
            raceSelect.name = 'website_race_select';

            const defaultOption = document.createElement('option');
            defaultOption.value = 'Race';
            defaultOption.textContent = 'All Races';
            raceSelect.appendChild(defaultOption);

            filters.race_select.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.label;
                raceSelect.appendChild(optionElement);
            });

            raceGroup.appendChild(raceLabel);
            raceGroup.appendChild(raceSelect);
            container.appendChild(raceGroup);
        }

        // Slot Filter
        if (filters.slot_select) {
            const slotGroup = document.createElement('div');
            slotGroup.className = 'filter-group';

            const slotLabel = document.createElement('label');
            slotLabel.textContent = 'Slot:';
            slotLabel.setAttribute('for', 'website-slot_select');

            const slotSelect = document.createElement('select');
            slotSelect.id = 'website-slot_select';
            slotSelect.name = 'website_slot_select';

            const defaultOption = document.createElement('option');
            defaultOption.value = 'Slot';
            defaultOption.textContent = 'All Slots';
            slotSelect.appendChild(defaultOption);

            filters.slot_select.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.label;
                slotSelect.appendChild(optionElement);
            });

            slotGroup.appendChild(slotLabel);
            slotGroup.appendChild(slotSelect);
            container.appendChild(slotGroup);
        }

        // Checkboxes for special effects
        const effectsGroup = document.createElement('div');
        effectsGroup.className = 'filter-group';
        effectsGroup.innerHTML = `
            <label>Special Effects:</label>
            <div class="checkbox-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="website-has_proc" name="website_has_proc">
                    Has Proc
                </label>
                <label class="checkbox-label">
                    <input type="checkbox" id="website-has_click" name="website_has_click">
                    Has Click Effect
                </label>
                <label class="checkbox-label">
                    <input type="checkbox" id="website-has_focus" name="website_has_focus">
                    Has Focus Effect
                </label>
                <label class="checkbox-label">
                    <input type="checkbox" id="website-has_worn" name="website_has_worn">
                    Has Worn Effect
                </label>
            </div>
        `;
        container.appendChild(effectsGroup);

        // Effect Name Filter
        const effectNameGroup = document.createElement('div');
        effectNameGroup.className = 'filter-group';
        effectNameGroup.innerHTML = `
            <label for="website-effect_name">Effect Name:</label>
            <input type="text" id="website-effect_name" name="website_effect_name" placeholder="Enter effect name...">
        `;
        container.appendChild(effectNameGroup);
    }

    formatFilterName(name) {
        return name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    async performSearch() {
        this.showLoading(true);
        
        try {
            const formData = this.collectFormData();
            const response = await fetch('/api/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            
            if (result.success) {
                this.currentResults = result.data.items || [];
                this.displayResults(result.data);
                this.showToast('Search completed successfully', 'success');
            } else {
                this.showToast(result.error || 'Search failed', 'error');
            }
        } catch (error) {
            console.error('Search error:', error);
            this.showToast('Search failed: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    collectFormData() {
        const form = document.getElementById('search-form');
        const formData = new FormData(form);
        const data = {};

        // Collect basic form data
        for (let [key, value] = formData.entries()) {
            if (value && value.trim && value.trim()) {
                data[key] = value;
            } else if (value && !value.trim) {
                // Handle non-string values (like checkboxes)
                data[key] = value;
            }
        }

        // Handle checkboxes that might not be in FormData if unchecked
        const checkboxes = form.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                data[checkbox.name] = checkbox.value || 'y';
            }
        });

        // Handle special cases for custom filters
        if (data.level_min || data.level_max) {
            data.level_range = {
                min: parseInt(data.level_min) || 0,
                max: parseInt(data.level_max) || 999
            };
            delete data.level_min;
            delete data.level_max;
        }

        if (data.exclude_keywords) {
            data.exclude_keywords = data.exclude_keywords.split(',').map(k => k.trim());
        }

        if (data.sort_field) {
            data.custom_sort = {
                field: data.sort_field,
                reverse: document.getElementById('sort-reverse').checked
            };
            delete data.sort_field;
            delete data.sort_reverse;
        }

        if (data.min_value_field && data.min_value) {
            data.min_value = {
                field: data.min_value_field,
                value: parseFloat(data.min_value)
            };
            delete data.min_value_field;
        }

        if (data.max_value_field && data.max_value) {
            data.max_value = {
                field: data.max_value_field,
                value: parseFloat(data.max_value)
            };
            delete data.max_value_field;
        }

        // Handle website-specific data mapping
        if (data.query && !data.website_item_name) {
            data.website_item_name = data.query;
        }

        return data;
    }

    displayResults(data) {
        const resultsSection = document.getElementById('results-section');
        const resultsGrid = document.getElementById('results-grid');
        const resultsCount = document.getElementById('results-count');
        const exportButton = document.getElementById('export-results');

        if (!data.items || data.items.length === 0) {
            resultsSection.classList.add('hidden');
            this.showToast('No items found matching your criteria', 'warning');
            return;
        }

        // Update results count
        resultsCount.textContent = `${data.total_count || data.items.length} items found`;
        
        // Enable export button
        exportButton.disabled = false;

        // Display statistics
        if (data.statistics) {
            this.displayStatistics(data.statistics);
        }

        // Display items
        this.displayItems(data.items);

        // Show results section
        resultsSection.classList.remove('hidden');

        // Setup pagination if needed
        this.setupPagination(data.items.length);
    }

    displayItems(items) {
        const resultsGrid = document.getElementById('results-grid');
        resultsGrid.innerHTML = '';

        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageItems = items.slice(startIndex, endIndex);

        pageItems.forEach(item => {
            const itemElement = this.createItemElement(item);
            resultsGrid.appendChild(itemElement);
        });
    }

    createItemElement(item) {
        const div = document.createElement('div');
        div.className = 'result-item';

        // Add item image if available
        if (item.image_url) {
            const img = document.createElement('img');
            img.src = item.image_url;
            img.alt = item.name || 'Item';
            img.className = 'result-item-image';
            img.onerror = () => {
                img.style.display = 'none';
            };
            div.appendChild(img);
        }

        const content = document.createElement('div');
        content.className = 'result-item-content';

        const name = document.createElement('div');
        name.className = 'result-name';
        name.textContent = item.name || 'Unknown Item';

        const description = document.createElement('div');
        description.className = 'result-description';
        description.textContent = item.description || 'Click to view details on PQDI';

        // Add item ID if available
        if (item.id) {
            const idSpan = document.createElement('span');
            idSpan.className = 'result-item-id';
            idSpan.textContent = `ID: ${item.id}`;
            description.appendChild(document.createElement('br'));
            description.appendChild(idSpan);
        }

        const stats = document.createElement('div');
        stats.className = 'result-stats';

        // Add any available stats from the item
        const statsToShow = ['level', 'damage', 'ac', 'price', 'hp', 'mana'];
        statsToShow.forEach(statName => {
            if (item[statName] !== undefined && item[statName] !== null && item[statName] !== '') {
                const statElement = document.createElement('div');
                statElement.className = 'result-stat';

                const label = document.createElement('span');
                label.className = 'result-stat-label';
                label.textContent = this.formatFilterName(statName);

                const value = document.createElement('span');
                value.className = 'result-stat-value';
                value.textContent = this.formatStatValue(item[statName], statName);

                statElement.appendChild(label);
                statElement.appendChild(value);
                stats.appendChild(statElement);
            }
        });

        content.appendChild(name);
        content.appendChild(description);
        content.appendChild(stats);
        div.appendChild(content);

        // Add click handler to open item on PQDI
        if (item.url) {
            div.style.cursor = 'pointer';
            div.addEventListener('click', () => {
                window.open(item.url, '_blank');
            });
        }

        return div;
    }

    formatStatValue(value, statName) {
        if (statName === 'price' && typeof value === 'number') {
            return value.toLocaleString();
        }
        return value.toString();
    }

    displayStatistics(statistics) {
        const statisticsContent = document.getElementById('statistics-content');
        if (!statisticsContent) return;

        statisticsContent.innerHTML = '';

        // Total count
        this.addStatItem(statisticsContent, 'Total Items', statistics.total_count);

        // Categories
        if (statistics.categories) {
            const topCategories = Object.entries(statistics.categories)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 3);
            
            topCategories.forEach(([category, count]) => {
                this.addStatItem(statisticsContent, `${category}`, count);
            });
        }

        // Level distribution
        if (statistics.level_distribution && statistics.level_distribution.average) {
            this.addStatItem(statisticsContent, 'Avg Level', Math.round(statistics.level_distribution.average));
        }

        // Price distribution
        if (statistics.price_distribution && statistics.price_distribution.average) {
            this.addStatItem(statisticsContent, 'Avg Price', Math.round(statistics.price_distribution.average));
        }
    }

    addStatItem(container, label, value) {
        const statItem = document.createElement('div');
        statItem.className = 'stat-item';
        
        const statLabel = document.createElement('div');
        statLabel.className = 'stat-label';
        statLabel.textContent = label;
        
        const statValue = document.createElement('div');
        statValue.className = 'stat-value';
        statValue.textContent = typeof value === 'number' ? value.toLocaleString() : value;
        
        statItem.appendChild(statLabel);
        statItem.appendChild(statValue);
        container.appendChild(statItem);
    }

    setupPagination(totalItems) {
        const pagination = document.getElementById('pagination');
        const totalPages = Math.ceil(totalItems / this.itemsPerPage);
        
        if (totalPages <= 1) {
            pagination.classList.add('hidden');
            return;
        }

        pagination.classList.remove('hidden');
        pagination.innerHTML = '';

        // Previous button
        const prevBtn = this.createPaginationButton('Previous', this.currentPage > 1, () => {
            this.currentPage--;
            this.displayItems(this.currentResults);
            this.setupPagination(totalItems);
        });
        pagination.appendChild(prevBtn);

        // Page numbers
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = this.createPaginationButton(i.toString(), true, () => {
                this.currentPage = i;
                this.displayItems(this.currentResults);
                this.setupPagination(totalItems);
            });
            
            if (i === this.currentPage) {
                pageBtn.classList.add('active');
            }
            
            pagination.appendChild(pageBtn);
        }

        // Next button
        const nextBtn = this.createPaginationButton('Next', this.currentPage < totalPages, () => {
            this.currentPage++;
            this.displayItems(this.currentResults);
            this.setupPagination(totalItems);
        });
        pagination.appendChild(nextBtn);
    }

    createPaginationButton(text, enabled, onClick) {
        const button = document.createElement('button');
        button.className = 'pagination-btn';
        button.textContent = text;
        button.disabled = !enabled;
        
        if (enabled) {
            button.addEventListener('click', onClick);
        }
        
        return button;
    }

    clearFilters() {
        const form = document.getElementById('search-form');
        form.reset();
        
        // Hide results
        const resultsSection = document.getElementById('results-section');
        resultsSection.classList.add('hidden');
        
        // Disable export button
        const exportButton = document.getElementById('export-results');
        exportButton.disabled = true;
        
        this.showToast('Filters cleared', 'success');
    }

    async exportResults() {
        if (!this.currentResults.length) {
            this.showToast('No results to export', 'warning');
            return;
        }

        try {
            const response = await fetch('/api/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    items: this.currentResults,
                    format: 'json'
                })
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `search_results_${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                this.showToast('Results exported successfully', 'success');
            } else {
                throw new Error('Export failed');
            }
        } catch (error) {
            console.error('Export error:', error);
            this.showToast('Export failed: ' + error.message, 'error');
        }
    }

    toggleView() {
        this.isGridView = !this.isGridView;
        const toggleButton = document.getElementById('toggle-view');
        const resultsGrid = document.getElementById('results-grid');
        
        if (this.isGridView) {
            toggleButton.innerHTML = '<i class="fas fa-list"></i> List View';
            resultsGrid.className = 'results-grid';
        } else {
            toggleButton.innerHTML = '<i class="fas fa-th"></i> Grid View';
            resultsGrid.className = 'results-list';
        }
    }

    toggleCollapse(element) {
        const target = document.getElementById(element.dataset.target);
        const icon = element.querySelector('i');
        
        if (target.classList.contains('collapsed')) {
            target.classList.remove('collapsed');
            icon.className = 'fas fa-chevron-up';
        } else {
            target.classList.add('collapsed');
            icon.className = 'fas fa-chevron-down';
        }
    }

    showItemDetails(item) {
        // Create modal or detailed view for item
        console.log('Item details:', item);
        // This could open a modal with detailed item information
    }

    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (show) {
            overlay.classList.remove('hidden');
        } else {
            overlay.classList.add('hidden');
        }
    }

    showToast(message, type = 'info') {
        const container = document.getElementById('toast-container');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        container.appendChild(toast);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);

        // Click to dismiss
        toast.addEventListener('click', () => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        });
    }
}

// Initialize the application
function initializeApp() {
    window.gameSearchApp = new GameItemSearchApp();
}

// Export for global access
window.initializeApp = initializeApp;
