#!/usr/bin/env python3
"""
<PERSON><PERSON>t to analyze the PQDI form structure
"""

import requests
from bs4 import BeautifulSoup
import json

def analyze_pqdi_form():
    """Analyze the PQDI items page form structure"""
    
    url = "https://www.pqdi.cc/items"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Find all forms
        forms = soup.find_all('form')
        print(f"Found {len(forms)} forms on the page")
        
        for i, form in enumerate(forms):
            form_id = form.get('id', f'form_{i}')
            print(f"\n=== FORM {i+1}: {form_id} ===")
            
            # Skip the searchForm as requested
            if form_id == 'searchForm':
                print("Skipping searchForm as requested")
                continue
            
            # Get form attributes
            action = form.get('action', '')
            method = form.get('method', 'GET')
            print(f"Action: {action}")
            print(f"Method: {method}")
            
            # Find all input elements
            inputs = form.find_all(['input', 'select', 'textarea'])
            print(f"Found {len(inputs)} form elements:")
            
            form_structure = {
                'action': action,
                'method': method,
                'elements': []
            }
            
            for input_elem in inputs:
                element_info = {
                    'tag': input_elem.name,
                    'type': input_elem.get('type', ''),
                    'name': input_elem.get('name', ''),
                    'id': input_elem.get('id', ''),
                    'value': input_elem.get('value', ''),
                    'placeholder': input_elem.get('placeholder', ''),
                    'required': input_elem.has_attr('required')
                }
                
                # For select elements, get options
                if input_elem.name == 'select':
                    options = []
                    for option in input_elem.find_all('option'):
                        options.append({
                            'value': option.get('value', ''),
                            'text': option.get_text().strip()
                        })
                    element_info['options'] = options
                
                form_structure['elements'].append(element_info)
                
                print(f"  - {element_info['tag']}: {element_info['name']} ({element_info['type']})")
                if element_info.get('options'):
                    print(f"    Options: {len(element_info['options'])} items")
            
            # Save the form structure
            if form_id != 'searchForm':
                with open(f'pqdi_form_structure.json', 'w') as f:
                    json.dump(form_structure, f, indent=2)
                print(f"\nForm structure saved to pqdi_form_structure.json")
                
                return form_structure
    
    except Exception as e:
        print(f"Error analyzing form: {e}")
        return None

if __name__ == "__main__":
    analyze_pqdi_form()
